import { getDomainConfig } from '@/lib/server/domain'
import { ImageResponse } from 'next/og'

// Image metadata
export const size = {
  width: 32,
  height: 32,
}
export const contentType = 'image/png'

// Image generation
export default async function Icon(): Promise<ImageResponse> {
  const domainConfig = await getDomainConfig()

  // Get branding data from domain config
  const brandName = domainConfig?.branding?.name || process.env.NEXT_PUBLIC_APP_NAME || 'AutoPAY'
  const faviconUrl = domainConfig?.branding?.favicon_url

  // If favicon URL is available, try to use it
  if (faviconUrl) {
    try {
      // Fetch the favicon image
      const faviconResponse = await fetch(faviconUrl)
      if (faviconResponse.ok) {
        const faviconBuffer = await faviconResponse.arrayBuffer()
        const faviconBase64 = Buffer.from(faviconBuffer).toString('base64')
        const faviconMimeType = faviconResponse.headers.get('content-type') || 'image/png'

        return new ImageResponse(
          (
            <div
              style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'white',
              }}>
              <img
                src={`data:${faviconMimeType};base64,${faviconBase64}`}
                alt={brandName}
                style={{
                  width: '28px',
                  height: '28px',
                  objectFit: 'contain',
                }}
              />
            </div>
          ),
          {
            ...size,
          }
        )
      }
    } catch (error) {
      console.warn('Failed to fetch favicon for icon generation:', error)
      // Fall through to text-based icon
    }
  }

  // Fallback to text-based icon using first letter of brand name
  const firstLetter = brandName.charAt(0).toUpperCase()

  return new ImageResponse(
    (
      <div
        style={{
          fontSize: 20,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold',
          fontFamily: 'system-ui, sans-serif',
        }}>
        {firstLetter}
      </div>
    ),
    {
      ...size,
    }
  )
}
